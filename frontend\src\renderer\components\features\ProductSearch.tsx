import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';
import * as Select from '@radix-ui/react-select';
import { 
  Search, 
  Filter, 
  X, 
  ChevronDown, 
  Check,
  Package,
  Eye,
  EyeOff,
  ShoppingCart
} from 'lucide-react';
import { Button } from '../common/Button';
import { useProductStore } from '../../store/productStore';
import { apiService } from '../../services/api.service';
import { 
  searchFormSchema, 
  SearchFormData 
} from '../../lib/validations/product.validation';
import { cn } from '../../lib/utils';

interface ProductSearchProps {
  className?: string;
}

interface ProductFiltersProps {
  className?: string;
}

export const ProductSearch: React.FC<ProductSearchProps> = ({ className }) => {
  const { productFilters, setProductFilters } = useProductStore();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset
  } = useForm<SearchFormData>({
    resolver: zodResolver(searchFormSchema),
    defaultValues: {
      query: productFilters.search
    }
  });

  const searchQuery = watch('query');

  // Debounced search
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setProductFilters({ search: searchQuery || '' });
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, setProductFilters]);

  const handleClear = () => {
    reset({ query: '' });
    setProductFilters({ search: '' });
  };

  return (
    <div className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <input
          {...register('query')}
          type="text"
          placeholder="Ürün adı, kodu veya barkod ile ara..."
          className={cn(
            "w-full pl-10 pr-10 py-2 border border-gray-600 rounded-lg text-sm",
            "bg-gray-700 text-white placeholder-gray-400",
            "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          )}
        />
        {searchQuery && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-gray-400 hover:text-gray-300"
          >
            <X className="w-3 h-3" />
          </Button>
        )}
      </div>
    </div>
  );
};

export const ProductFilters: React.FC<ProductFiltersProps> = ({ className }) => {
  const { 
    productFilters, 
    setProductFilters,
    setProductPagination 
  } = useProductStore();

  // Load categories for filter
  const { data: categoriesData } = useQuery({
    queryKey: ['categories', { limit: 100 }],
    queryFn: () => apiService.getCategories({ limit: 100 }),
    staleTime: 10 * 60 * 1000 // 10 minutes
  });

  const handleCategoryChange = (categoryId: string) => {
    setProductFilters({ categoryId: categoryId === 'all' ? '' : categoryId });
    setProductPagination({ page: 1 }); // Reset to first page
  };

  const handleAvailableChange = (available: string) => {
    const value = available === 'all' ? undefined : available === 'true';
    setProductFilters({ available: value });
    setProductPagination({ page: 1 });
  };

  const handleSellableChange = (sellable: string) => {
    const value = sellable === 'all' ? undefined : sellable === 'true';
    setProductFilters({ sellable: value });
    setProductPagination({ page: 1 });
  };

  const handleTrackStockChange = (trackStock: string) => {
    const value = trackStock === 'all' ? undefined : trackStock === 'true';
    setProductFilters({ trackStock: value });
    setProductPagination({ page: 1 });
  };

  const clearFilters = () => {
    setProductFilters({
      categoryId: '',
      available: undefined,
      sellable: undefined,
      trackStock: undefined
    });
    setProductPagination({ page: 1 });
  };

  const hasActiveFilters = 
    productFilters.categoryId || 
    productFilters.available !== undefined || 
    productFilters.sellable !== undefined || 
    productFilters.trackStock !== undefined;

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">Filtreler</span>
        </div>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            Temizle
          </Button>
        )}
      </div>

      <div className="space-y-3">
        {/* Category Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Kategori
          </label>
          <Select.Root
            value={productFilters.categoryId || 'all'}
            onValueChange={handleCategoryChange}
          >
            <Select.Trigger className={cn(
              "w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              "flex items-center justify-between"
            )}>
              <Select.Value />
              <Select.Icon>
                <ChevronDown className="w-4 h-4" />
              </Select.Icon>
            </Select.Trigger>
            <Select.Portal>
              <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                <Select.Viewport className="p-1">
                  <Select.Item
                    value="all"
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                  >
                    <Select.ItemText>Tüm Kategoriler</Select.ItemText>
                    <Select.ItemIndicator>
                      <Check className="w-4 h-4" />
                    </Select.ItemIndicator>
                  </Select.Item>
                  {categoriesData?.data?.map((category) => (
                    <Select.Item
                      key={category.id}
                      value={category.id}
                      className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: category.color || '#6B7280' }}
                        />
                        <Select.ItemText>{category.name}</Select.ItemText>
                      </div>
                      <Select.ItemIndicator>
                        <Check className="w-4 h-4" />
                      </Select.ItemIndicator>
                    </Select.Item>
                  ))}
                </Select.Viewport>
              </Select.Content>
            </Select.Portal>
          </Select.Root>
        </div>

        {/* Available Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Mevcut Durum
          </label>
          <Select.Root
            value={
              productFilters.available === undefined 
                ? 'all' 
                : productFilters.available.toString()
            }
            onValueChange={handleAvailableChange}
          >
            <Select.Trigger className={cn(
              "w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              "flex items-center justify-between"
            )}>
              <Select.Value />
              <Select.Icon>
                <ChevronDown className="w-4 h-4" />
              </Select.Icon>
            </Select.Trigger>
            <Select.Portal>
              <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <Select.Viewport className="p-1">
                  <Select.Item
                    value="all"
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                  >
                    <Select.ItemText>Tümü</Select.ItemText>
                    <Select.ItemIndicator>
                      <Check className="w-4 h-4" />
                    </Select.ItemIndicator>
                  </Select.Item>
                  <Select.Item
                    value="true"
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <Eye className="w-3 h-3 text-green-600" />
                      <Select.ItemText>Mevcut</Select.ItemText>
                    </div>
                    <Select.ItemIndicator>
                      <Check className="w-4 h-4" />
                    </Select.ItemIndicator>
                  </Select.Item>
                  <Select.Item
                    value="false"
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <EyeOff className="w-3 h-3 text-red-600" />
                      <Select.ItemText>Mevcut Değil</Select.ItemText>
                    </div>
                    <Select.ItemIndicator>
                      <Check className="w-4 h-4" />
                    </Select.ItemIndicator>
                  </Select.Item>
                </Select.Viewport>
              </Select.Content>
            </Select.Portal>
          </Select.Root>
        </div>

        {/* Sellable Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Satış Durum
          </label>
          <Select.Root
            value={
              productFilters.sellable === undefined 
                ? 'all' 
                : productFilters.sellable.toString()
            }
            onValueChange={handleSellableChange}
          >
            <Select.Trigger className={cn(
              "w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              "flex items-center justify-between"
            )}>
              <Select.Value />
              <Select.Icon>
                <ChevronDown className="w-4 h-4" />
              </Select.Icon>
            </Select.Trigger>
            <Select.Portal>
              <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <Select.Viewport className="p-1">
                  <Select.Item
                    value="all"
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                  >
                    <Select.ItemText>Tümü</Select.ItemText>
                    <Select.ItemIndicator>
                      <Check className="w-4 h-4" />
                    </Select.ItemIndicator>
                  </Select.Item>
                  <Select.Item
                    value="true"
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <ShoppingCart className="w-3 h-3 text-green-600" />
                      <Select.ItemText>Satışta</Select.ItemText>
                    </div>
                    <Select.ItemIndicator>
                      <Check className="w-4 h-4" />
                    </Select.ItemIndicator>
                  </Select.Item>
                  <Select.Item
                    value="false"
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <X className="w-3 h-3 text-red-600" />
                      <Select.ItemText>Satışta Değil</Select.ItemText>
                    </div>
                    <Select.ItemIndicator>
                      <Check className="w-4 h-4" />
                    </Select.ItemIndicator>
                  </Select.Item>
                </Select.Viewport>
              </Select.Content>
            </Select.Portal>
          </Select.Root>
        </div>

        {/* Track Stock Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Stok Takibi
          </label>
          <Select.Root
            value={
              productFilters.trackStock === undefined 
                ? 'all' 
                : productFilters.trackStock.toString()
            }
            onValueChange={handleTrackStockChange}
          >
            <Select.Trigger className={cn(
              "w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              "flex items-center justify-between"
            )}>
              <Select.Value />
              <Select.Icon>
                <ChevronDown className="w-4 h-4" />
              </Select.Icon>
            </Select.Trigger>
            <Select.Portal>
              <Select.Content className="bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <Select.Viewport className="p-1">
                  <Select.Item
                    value="all"
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                  >
                    <Select.ItemText>Tümü</Select.ItemText>
                    <Select.ItemIndicator>
                      <Check className="w-4 h-4" />
                    </Select.ItemIndicator>
                  </Select.Item>
                  <Select.Item
                    value="true"
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <Package className="w-3 h-3 text-blue-600" />
                      <Select.ItemText>Stok Takipli</Select.ItemText>
                    </div>
                    <Select.ItemIndicator>
                      <Check className="w-4 h-4" />
                    </Select.ItemIndicator>
                  </Select.Item>
                  <Select.Item
                    value="false"
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <X className="w-3 h-3 text-gray-600" />
                      <Select.ItemText>Stok Takipsiz</Select.ItemText>
                    </div>
                    <Select.ItemIndicator>
                      <Check className="w-4 h-4" />
                    </Select.ItemIndicator>
                  </Select.Item>
                </Select.Viewport>
              </Select.Content>
            </Select.Portal>
          </Select.Root>
        </div>
      </div>
    </div>
  );
};
