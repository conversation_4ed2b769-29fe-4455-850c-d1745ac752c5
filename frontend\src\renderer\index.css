@tailwind base;
@tailwind components;
@tailwind utilities;

/* 1️⃣ Electron resets */
html, body, #root {
  @apply w-screen h-screen overflow-hidden;
}

/* 2️⃣ Viewport fix for kiosk */
body {
  margin: 0;
  padding: 0;
  -webkit-app-region: no-drag;   /* optional: allow drag on title-bar later */
  user-select: none;             /* kiosk mode */
}

/* 3️⃣ Tailwind helper for kiosk screens */
.kiosk {
  @apply w-screen h-screen;
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 🎨 ATROPOS POS UTILITY CLASSES */
  .atropos-input {
    @apply bg-input border border-input-border text-input-text placeholder:text-input-placeholder;
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:border-input-border-focus;
  }

  .atropos-card {
    @apply bg-card border border-card-border text-card-foreground;
  }

  .atropos-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary-600;
  }

  .atropos-button-secondary {
    @apply bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300;
  }

  .atropos-text-primary {
    @apply text-gray-900;
  }

  .atropos-text-secondary {
    @apply text-gray-600;
  }

  .atropos-text-muted {
    @apply text-gray-500;
  }
}

@layer base {
  html, body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Custom scrollbar for webkit browsers */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* Radix UI Dialog animations */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }

  @keyframes zoomIn {
    from {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  @keyframes zoomOut {
    from {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    to {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.95);
    }
  }

  .animate-in {
    animation-duration: 200ms;
    animation-fill-mode: both;
  }

  .animate-out {
    animation-duration: 150ms;
    animation-fill-mode: both;
  }

  .fade-in-0 {
    animation-name: fadeIn;
  }

  .fade-out-0 {
    animation-name: fadeOut;
  }

  .zoom-in-95 {
    animation-name: zoomIn;
  }

  .zoom-out-95 {
    animation-name: zoomOut;
  }

  .slide-in-from-left-1\/2 {
    animation-name: zoomIn;
  }

  .slide-in-from-top-48 {
    animation-name: zoomIn;
  }

  .slide-out-to-left-1\/2 {
    animation-name: zoomOut;
  }

  .slide-out-to-top-48 {
    animation-name: zoomOut;
  }
}
